﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace DDD.DomainLayer
{
    public abstract class Entity<K>
        where K: IEquatable<K>
    {

        public virtual K Id { get; protected set; } = default!;
        public bool IsTransient()
        {
            return Object.Equals(Id, default(K));
            
        }
        #region domain events handling
        private List<IDomainEvent>? _domainEvents;

        /// <summary>
        /// Domain events that have occurred on this entity
        /// </summary>
        public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents?.AsReadOnly() ?? new List<IDomainEvent>().AsReadOnly();

        /// <summary>
        /// Add a domain event to this entity
        /// </summary>
        /// <param name="domainEvent">The domain event to add</param>
        public void AddDomainEvent(IDomainEvent domainEvent)
        {
            _domainEvents ??= new List<IDomainEvent>();
            _domainEvents.Add(domainEvent);
        }

        /// <summary>
        /// Remove a specific domain event from this entity
        /// </summary>
        /// <param name="domainEvent">The domain event to remove</param>
        public void RemoveDomainEvent(IDomainEvent domainEvent)
        {
            _domainEvents?.Remove(domainEvent);
        }

        /// <summary>
        /// Clear all domain events from this entity
        /// </summary>
        public void ClearDomainEvents()
        {
            _domainEvents?.Clear();
        }

        /// <summary>
        /// Check if this entity has any domain events
        /// </summary>
        public bool HasDomainEvents => _domainEvents?.Count > 0;
        #endregion
        #region override Equal
        public override bool Equals(object? obj)
        {
            return obj != null && obj is Entity<K> entity &&
              Equals(entity); 
        }

        public bool Equals(Entity<K> other)
        {
            if (other.IsTransient() || this.IsTransient())
                return false;
            return Object.Equals(Id, other.Id);
        }

        int? _requestedHashCode;
        public override int GetHashCode()
        {
            if (!IsTransient())
            {
                if (!_requestedHashCode.HasValue)
                    _requestedHashCode = HashCode.Combine(Id);
                return _requestedHashCode.Value;
            }
            else
                return base.GetHashCode();
        }
        public static bool operator ==(Entity<K> left, Entity<K> right)
        {
            if (Object.Equals(left, null))
                return (Object.Equals(right, null));
            else
                return left.Equals(right);
        }
        public static bool operator !=(Entity<K> left, Entity<K> right)
        {
            return !(left == right);
        }
        #endregion

    }
}
