using System;
using DDD.DomainLayer.Events;

namespace DDD.DomainLayer.Entities
{
    /// <summary>
    /// User entity for the car sharing platform
    /// </summary>
    public class User : Entity<Guid>
    {
        private User() { } // For EF Core

        public User(string email, string firstName, string lastName, string phoneNumber, string drivingLicenseNumber)
        {
            Id = Guid.NewGuid();
            Email = email ?? throw new ArgumentNullException(nameof(email));
            FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
            LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
            PhoneNumber = phoneNumber ?? throw new ArgumentNullException(nameof(phoneNumber));
            DrivingLicenseNumber = drivingLicenseNumber ?? throw new ArgumentNullException(nameof(drivingLicenseNumber));
            CreatedAt = DateTime.UtcNow;
            IsActive = true;
            IsVerified = false;
            RegistrationStatus = UserRegistrationStatus.Pending;

            // Raise domain event when user is created
            AddDomainEvent(new UserRegisteredDomainEvent(Id, Email, FirstName, LastName, PhoneNumber));
        }

        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; private set; } = string.Empty;

        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; private set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; private set; } = string.Empty;

        /// <summary>
        /// User's phone number
        /// </summary>
        public string PhoneNumber { get; private set; } = string.Empty;

        /// <summary>
        /// User's driving license number
        /// </summary>
        public string DrivingLicenseNumber { get; private set; } = string.Empty;

        /// <summary>
        /// When the user was created
        /// </summary>
        public DateTime CreatedAt { get; private set; }

        /// <summary>
        /// Whether the user account is active
        /// </summary>
        public bool IsActive { get; private set; }

        /// <summary>
        /// Whether the user's identity and license are verified
        /// </summary>
        public bool IsVerified { get; private set; }

        /// <summary>
        /// User's registration status
        /// </summary>
        public UserRegistrationStatus RegistrationStatus { get; private set; }

        /// <summary>
        /// User's full name
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Verify the user's identity and driving license
        /// </summary>
        public void VerifyUser()
        {
            if (!IsVerified)
            {
                IsVerified = true;
                RegistrationStatus = UserRegistrationStatus.Verified;
                
                AddDomainEvent(new UserVerifiedDomainEvent(Id, Email, FullName));
            }
        }

        /// <summary>
        /// Reject user verification
        /// </summary>
        /// <param name="reason">Reason for rejection</param>
        public void RejectVerification(string reason)
        {
            if (RegistrationStatus == UserRegistrationStatus.Pending)
            {
                RegistrationStatus = UserRegistrationStatus.Rejected;
                
                AddDomainEvent(new UserVerificationRejectedDomainEvent(Id, Email, reason));
            }
        }

        /// <summary>
        /// Deactivate the user account
        /// </summary>
        /// <param name="reason">Reason for deactivation</param>
        public void DeactivateAccount(string reason)
        {
            if (IsActive)
            {
                IsActive = false;
                
                AddDomainEvent(new UserAccountDeactivatedDomainEvent(Id, Email, reason));
            }
        }

        /// <summary>
        /// Activate the user account
        /// </summary>
        public void ActivateAccount()
        {
            if (!IsActive)
            {
                IsActive = true;
                
                AddDomainEvent(new UserAccountActivatedDomainEvent(Id, Email));
            }
        }

        /// <summary>
        /// Update user's contact information
        /// </summary>
        /// <param name="newEmail">New email address</param>
        /// <param name="newPhoneNumber">New phone number</param>
        public void UpdateContactInfo(string newEmail, string newPhoneNumber)
        {
            var emailChanged = Email != newEmail;
            var phoneChanged = PhoneNumber != newPhoneNumber;

            if (emailChanged || phoneChanged)
            {
                var oldEmail = Email;
                var oldPhone = PhoneNumber;

                Email = newEmail ?? throw new ArgumentNullException(nameof(newEmail));
                PhoneNumber = newPhoneNumber ?? throw new ArgumentNullException(nameof(newPhoneNumber));

                AddDomainEvent(new UserContactInfoUpdatedDomainEvent(Id, oldEmail, newEmail, oldPhone, newPhoneNumber));
            }
        }

        public override string ToString()
        {
            return $"User: {Id} - {FullName} ({Email}) - Status: {RegistrationStatus}, Active: {IsActive}";
        }
    }

    /// <summary>
    /// User registration status enumeration
    /// </summary>
    public enum UserRegistrationStatus
    {
        Pending,
        Verified,
        Rejected
    }
}
