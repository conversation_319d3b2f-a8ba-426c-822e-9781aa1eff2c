﻿using DDD.DomainLayer;
using DDD.ApplicationLayer;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using System.Threading;
namespace DBDriver
{
    internal class MainDbContext : DbContext, IUnitOfWork
    {
        private readonly IDomainEventDispatcher? _domainEventDispatcher;

        public MainDbContext(DbContextOptions options)
        : base(options)
        {
        }

        public MainDbContext(DbContextOptions options, IDomainEventDispatcher domainEventDispatcher)
        : base(options)
        {
            _domainEventDispatcher = domainEventDispatcher;
        }
        protected override void OnModelCreating(ModelBuilder
        builder)
        {
        }
        #region IUnitOfWork Implementation
        public async Task<bool> SaveEntitiesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // Get domain events before saving
                var domainEvents = GetDomainEvents().ToList();

                // Save changes to database
                var result = await SaveChangesAsync(cancellationToken) > 0;

                // Dispatch domain events after successful save
                if (result && domainEvents.Any() && _domainEventDispatcher != null)
                {
                    await _domainEventDispatcher.DispatchAsync(domainEvents, cancellationToken);
                }

                // Clear domain events after dispatching
                ClearDomainEvents();

                return result;
            }
            catch (DbUpdateConcurrencyException ex)
            {
                throw new ConcurrencyException(ex);
            }
            catch (DbUpdateException ex)
            {
                throw new ConstraintViolationException(ex);
            }
        }

        public async Task StartAsync(IsolationLevel isolationLevel = IsolationLevel.ReadCommitted, CancellationToken cancellationToken = default)
        {
            await Database.BeginTransactionAsync(isolationLevel, cancellationToken);
        }

        public Task CommitAsync(CancellationToken cancellationToken = default)
        {
            Database.CommitTransaction();
            return Task.CompletedTask;
        }

        public Task RollbackAsync(CancellationToken cancellationToken = default)
        {
            Database.RollbackTransaction();
            return Task.CompletedTask;
        }

        public IEnumerable<IDomainEvent> GetDomainEvents()
        {
            var domainEvents = new List<IDomainEvent>();

            foreach (var entry in ChangeTracker.Entries())
            {
                if (entry.Entity is Entity<Guid> guidEntity && guidEntity.HasDomainEvents)
                {
                    domainEvents.AddRange(guidEntity.DomainEvents);
                }
                else if (entry.Entity is Entity<int> intEntity && intEntity.HasDomainEvents)
                {
                    domainEvents.AddRange(intEntity.DomainEvents);
                }
                else if (entry.Entity is Entity<string> stringEntity && stringEntity.HasDomainEvents)
                {
                    domainEvents.AddRange(stringEntity.DomainEvents);
                }
                // Add more entity types as needed
            }

            return domainEvents;
        }

        public void ClearDomainEvents()
        {
            foreach (var entry in ChangeTracker.Entries())
            {
                if (entry.Entity is Entity<Guid> guidEntity && guidEntity.HasDomainEvents)
                {
                    guidEntity.ClearDomainEvents();
                }
                else if (entry.Entity is Entity<int> intEntity && intEntity.HasDomainEvents)
                {
                    intEntity.ClearDomainEvents();
                }
                else if (entry.Entity is Entity<string> stringEntity && stringEntity.HasDomainEvents)
                {
                    stringEntity.ClearDomainEvents();
                }
                // Add more entity types as needed
            }
        }

        public bool HasDomainEvents => GetDomainEvents().Any();
        #endregion

    }
}
