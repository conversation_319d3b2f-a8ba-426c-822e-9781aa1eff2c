using System;
using DDD.DomainLayer.Events;

namespace DDD.DomainLayer.Entities
{
    /// <summary>
    /// Booking entity for car reservations
    /// </summary>
    public class Booking : Entity<Guid>
    {
        private Booking() { } // For EF Core

        public Booking(Guid userId, Guid vehicleId, DateTime startTime, DateTime endTime, string pickupLocation)
        {
            Id = Guid.NewGuid();
            UserId = userId;
            VehicleId = vehicleId;
            StartTime = startTime;
            EndTime = endTime;
            PickupLocation = pickupLocation ?? throw new ArgumentNullException(nameof(pickupLocation));
            Status = BookingStatus.Pending;
            CreatedAt = DateTime.UtcNow;

            // Calculate estimated cost (this would typically involve a pricing service)
            var duration = endTime - startTime;
            EstimatedCost = (decimal)duration.TotalHours * 25.00m; // $25/hour base rate

            // Raise domain event when booking is created
            AddDomainEvent(new BookingCreatedDomainEvent(Id, UserId, VehicleId, StartTime, EndTime, EstimatedCost));
        }

        /// <summary>
        /// ID of the user making the booking
        /// </summary>
        public Guid UserId { get; private set; }

        /// <summary>
        /// ID of the vehicle being booked
        /// </summary>
        public Guid VehicleId { get; private set; }

        /// <summary>
        /// Booking start time
        /// </summary>
        public DateTime StartTime { get; private set; }

        /// <summary>
        /// Booking end time
        /// </summary>
        public DateTime EndTime { get; private set; }

        /// <summary>
        /// Where the user will pick up the vehicle
        /// </summary>
        public string PickupLocation { get; private set; } = string.Empty;

        /// <summary>
        /// Where the user returned the vehicle (if completed)
        /// </summary>
        public string? ReturnLocation { get; private set; }

        /// <summary>
        /// Current status of the booking
        /// </summary>
        public BookingStatus Status { get; private set; }

        /// <summary>
        /// Estimated cost of the booking
        /// </summary>
        public decimal EstimatedCost { get; private set; }

        /// <summary>
        /// Actual cost of the booking (calculated after completion)
        /// </summary>
        public decimal? ActualCost { get; private set; }

        /// <summary>
        /// When the booking was created
        /// </summary>
        public DateTime CreatedAt { get; private set; }

        /// <summary>
        /// When the booking was confirmed
        /// </summary>
        public DateTime? ConfirmedAt { get; private set; }

        /// <summary>
        /// When the booking was cancelled
        /// </summary>
        public DateTime? CancelledAt { get; private set; }

        /// <summary>
        /// Reason for cancellation
        /// </summary>
        public string? CancellationReason { get; private set; }

        /// <summary>
        /// Duration of the booking
        /// </summary>
        public TimeSpan Duration => EndTime - StartTime;

        /// <summary>
        /// Confirm the booking
        /// </summary>
        public void Confirm()
        {
            if (Status != BookingStatus.Pending)
                throw new InvalidOperationException($"Cannot confirm booking {Id} with status {Status}");

            Status = BookingStatus.Confirmed;
            ConfirmedAt = DateTime.UtcNow;

            AddDomainEvent(new BookingConfirmedDomainEvent(Id, UserId, VehicleId, StartTime, EndTime));
        }

        /// <summary>
        /// Cancel the booking
        /// </summary>
        /// <param name="reason">Reason for cancellation</param>
        /// <param name="cancelledBy">Who cancelled the booking (User or System)</param>
        public void Cancel(string reason, string cancelledBy = "User")
        {
            if (Status == BookingStatus.Completed || Status == BookingStatus.Cancelled)
                throw new InvalidOperationException($"Cannot cancel booking {Id} with status {Status}");

            Status = BookingStatus.Cancelled;
            CancelledAt = DateTime.UtcNow;
            CancellationReason = reason;

            AddDomainEvent(new BookingCancelledDomainEvent(Id, UserId, VehicleId, reason, cancelledBy));
        }

        /// <summary>
        /// Start the booking (user picks up the vehicle)
        /// </summary>
        public void Start()
        {
            if (Status != BookingStatus.Confirmed)
                throw new InvalidOperationException($"Cannot start booking {Id} with status {Status}");

            if (DateTime.UtcNow < StartTime.AddMinutes(-15)) // Allow 15 minutes early pickup
                throw new InvalidOperationException($"Booking {Id} cannot be started before {StartTime.AddMinutes(-15)}");

            Status = BookingStatus.Active;

            AddDomainEvent(new BookingStartedDomainEvent(Id, UserId, VehicleId, DateTime.UtcNow));
        }

        /// <summary>
        /// Complete the booking
        /// </summary>
        /// <param name="returnLocation">Where the vehicle was returned</param>
        /// <param name="actualEndTime">When the booking actually ended</param>
        /// <param name="finalCost">The calculated final cost</param>
        public void Complete(string returnLocation, DateTime actualEndTime, decimal finalCost)
        {
            if (Status != BookingStatus.Active)
                throw new InvalidOperationException($"Cannot complete booking {Id} with status {Status}");

            Status = BookingStatus.Completed;
            ReturnLocation = returnLocation ?? throw new ArgumentNullException(nameof(returnLocation));
            ActualCost = finalCost;

            // Update end time if the booking was extended
            if (actualEndTime > EndTime)
            {
                EndTime = actualEndTime;
            }

            AddDomainEvent(new BookingCompletedDomainEvent(Id, UserId, VehicleId, actualEndTime, returnLocation, finalCost));
        }

        /// <summary>
        /// Mark booking as no-show if user doesn't pick up vehicle
        /// </summary>
        public void MarkAsNoShow()
        {
            if (Status != BookingStatus.Confirmed)
                throw new InvalidOperationException($"Cannot mark booking {Id} as no-show with status {Status}");

            if (DateTime.UtcNow < StartTime.AddMinutes(30)) // Grace period of 30 minutes
                throw new InvalidOperationException($"Cannot mark booking {Id} as no-show before grace period expires");

            Status = BookingStatus.NoShow;

            AddDomainEvent(new BookingNoShowDomainEvent(Id, UserId, VehicleId, StartTime));
        }

        /// <summary>
        /// Extend the booking end time
        /// </summary>
        /// <param name="newEndTime">New end time</param>
        /// <param name="additionalCost">Additional cost for the extension</param>
        public void Extend(DateTime newEndTime, decimal additionalCost)
        {
            if (Status != BookingStatus.Active)
                throw new InvalidOperationException($"Cannot extend booking {Id} with status {Status}");

            if (newEndTime <= EndTime)
                throw new ArgumentException("New end time must be after current end time", nameof(newEndTime));

            var oldEndTime = EndTime;
            EndTime = newEndTime;
            EstimatedCost += additionalCost;

            AddDomainEvent(new BookingExtendedDomainEvent(Id, UserId, VehicleId, oldEndTime, newEndTime, additionalCost));
        }

        public override string ToString()
        {
            return $"Booking: {Id} - User: {UserId}, Vehicle: {VehicleId}, Status: {Status}, Duration: {Duration}";
        }
    }

    /// <summary>
    /// Booking status enumeration
    /// </summary>
    public enum BookingStatus
    {
        Pending,
        Confirmed,
        Active,
        Completed,
        Cancelled,
        NoShow
    }
}
