using DDD.DomainLayer;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DDD.ApplicationLayer
{
    /// <summary>
    /// In-memory implementation of integration event bus for demonstration purposes
    /// In production, this would be replaced with a real message broker like RabbitMQ, Azure Service Bus, etc.
    /// </summary>
    public class InMemoryIntegrationEventBus : IIntegrationEventBus
    {
        private readonly ILogger<InMemoryIntegrationEventBus> _logger;
        private readonly List<IIntegrationEvent> _publishedEvents;

        public InMemoryIntegrationEventBus(ILogger<InMemoryIntegrationEventBus> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _publishedEvents = new List<IIntegrationEvent>();
        }

        /// <summary>
        /// Get all published events (for testing/demonstration purposes)
        /// </summary>
        public IReadOnlyCollection<IIntegrationEvent> PublishedEvents => _publishedEvents.AsReadOnly();

        public async Task PublishAsync(IIntegrationEvent integrationEvent, CancellationToken cancellationToken = default)
        {
            if (integrationEvent == null)
                throw new ArgumentNullException(nameof(integrationEvent));

            _logger.LogInformation(
                "Publishing integration event {EventType} with ID {EventId} from source {Source}",
                integrationEvent.EventType,
                integrationEvent.EventId,
                integrationEvent.Source);

            // Simulate async operation
            await Task.Delay(10, cancellationToken);

            _publishedEvents.Add(integrationEvent);

            _logger.LogInformation(
                "Successfully published integration event {EventType} with ID {EventId}",
                integrationEvent.EventType,
                integrationEvent.EventId);
        }

        public async Task PublishAsync(IEnumerable<IIntegrationEvent> integrationEvents, CancellationToken cancellationToken = default)
        {
            if (integrationEvents == null)
                throw new ArgumentNullException(nameof(integrationEvents));

            var events = integrationEvents.ToList();
            
            _logger.LogInformation("Publishing {EventCount} integration events", events.Count);

            foreach (var integrationEvent in events)
            {
                await PublishAsync(integrationEvent, cancellationToken);
            }

            _logger.LogInformation("Successfully published {EventCount} integration events", events.Count);
        }

        /// <summary>
        /// Clear all published events (for testing purposes)
        /// </summary>
        public void ClearPublishedEvents()
        {
            _publishedEvents.Clear();
        }
    }
}
