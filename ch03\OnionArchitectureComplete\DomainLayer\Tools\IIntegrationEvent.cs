using System;

namespace DDD.DomainLayer
{
    /// <summary>
    /// Marker interface for integration events that cross bounded contexts
    /// Integration events are used for communication between different microservices or bounded contexts
    /// </summary>
    public interface IIntegrationEvent : IEventNotification
    {
        /// <summary>
        /// The source service/context that published this event
        /// </summary>
        string Source { get; }

        /// <summary>
        /// The type of the event for routing and handling purposes
        /// </summary>
        string EventType { get; }

        /// <summary>
        /// Optional routing key for message brokers
        /// </summary>
        string? RoutingKey { get; }
    }
}
