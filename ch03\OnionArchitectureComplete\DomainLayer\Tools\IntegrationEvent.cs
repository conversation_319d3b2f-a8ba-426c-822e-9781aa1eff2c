using System;
using System.Collections.Generic;

namespace DDD.DomainLayer
{
    /// <summary>
    /// Base class for all integration events
    /// </summary>
    public abstract class IntegrationEvent : IIntegrationEvent
    {
        protected IntegrationEvent(string source)
        {
            EventId = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            Source = source ?? throw new ArgumentNullException(nameof(source));
            EventType = GetType().Name;
        }

        protected IntegrationEvent(Guid eventId, DateTime occurredOn, string source)
        {
            EventId = eventId;
            OccurredOn = occurredOn;
            Source = source ?? throw new ArgumentNullException(nameof(source));
            EventType = GetType().Name;
        }

        /// <summary>
        /// Unique identifier for the event
        /// </summary>
        public Guid EventId { get; private set; }

        /// <summary>
        /// When the event occurred
        /// </summary>
        public DateTime OccurredOn { get; private set; }

        /// <summary>
        /// The source service/context that published this event
        /// </summary>
        public string Source { get; private set; }

        /// <summary>
        /// The type of the event for routing and handling purposes
        /// </summary>
        public string EventType { get; private set; }

        /// <summary>
        /// Optional routing key for message brokers
        /// </summary>
        public string? RoutingKey { get; set; }

        /// <summary>
        /// Optional correlation ID for tracking related events across services
        /// </summary>
        public string? CorrelationId { get; set; }

        /// <summary>
        /// Optional causation ID for tracking the cause of this event
        /// </summary>
        public string? CausationId { get; set; }

        /// <summary>
        /// Version of the event schema for evolution purposes
        /// </summary>
        public virtual int Version => 1;

        /// <summary>
        /// Optional metadata for additional context
        /// </summary>
        public Dictionary<string, object>? Metadata { get; set; }

        public override bool Equals(object? obj)
        {
            if (obj is not IntegrationEvent other)
                return false;

            return EventId == other.EventId;
        }

        public override int GetHashCode()
        {
            return EventId.GetHashCode();
        }

        public static bool operator ==(IntegrationEvent? left, IntegrationEvent? right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(IntegrationEvent? left, IntegrationEvent? right)
        {
            return !Equals(left, right);
        }
    }
}
