using DDD.DomainLayer;
using DDD.DomainLayer.Events;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace DDD.ApplicationLayer.EventHandlers
{
    /// <summary>
    /// Handler for booking creation events
    /// </summary>
    public class BookingCreatedDomainEventHandler : IEventHandler<BookingCreatedDomainEvent>
    {
        private readonly ILogger<BookingCreatedDomainEventHandler> _logger;

        public BookingCreatedDomainEventHandler(ILogger<BookingCreatedDomainEventHandler> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(BookingCreatedDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling BookingCreatedDomainEvent for booking {BookingId}", domainEvent.BookingId);

            try
            {
                // Business logic for new bookings:
                // 1. Validate vehicle availability
                // 2. Check user eligibility
                // 3. Calculate pricing
                // 4. Send booking confirmation

                await ValidateBookingAsync(domainEvent, cancellationToken);
                await SendBookingNotificationAsync(domainEvent, cancellationToken);

                _logger.LogInformation("Successfully processed booking creation for {BookingId}", domainEvent.BookingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling BookingCreatedDomainEvent for booking {BookingId}: {ErrorMessage}", 
                    domainEvent.BookingId, ex.Message);
                throw;
            }
        }

        private async Task ValidateBookingAsync(BookingCreatedDomainEvent domainEvent, CancellationToken cancellationToken)
        {
            // Simulate validation logic
            await Task.Delay(50, cancellationToken);
            _logger.LogInformation("Booking validation completed for {BookingId}", domainEvent.BookingId);
        }

        private async Task SendBookingNotificationAsync(BookingCreatedDomainEvent domainEvent, CancellationToken cancellationToken)
        {
            // Simulate notification sending
            await Task.Delay(100, cancellationToken);
            _logger.LogInformation("Booking notification sent for {BookingId}", domainEvent.BookingId);
        }
    }

    /// <summary>
    /// Handler for booking confirmation events
    /// </summary>
    public class BookingConfirmedDomainEventHandler : IEventHandler<BookingConfirmedDomainEvent>
    {
        private readonly IIntegrationEventBus _integrationEventBus;
        private readonly ILogger<BookingConfirmedDomainEventHandler> _logger;

        public BookingConfirmedDomainEventHandler(
            IIntegrationEventBus integrationEventBus,
            ILogger<BookingConfirmedDomainEventHandler> logger)
        {
            _integrationEventBus = integrationEventBus ?? throw new ArgumentNullException(nameof(integrationEventBus));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(BookingConfirmedDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling BookingConfirmedDomainEvent for booking {BookingId}", domainEvent.BookingId);

            try
            {
                // Business logic for confirmed bookings:
                // 1. Reserve the vehicle
                // 2. Send confirmation email
                // 3. Publish integration event for external systems
                // 4. Schedule reminder notifications

                await ReserveVehicleAsync(domainEvent.VehicleId, domainEvent.BookingId, cancellationToken);

                // Publish integration event for external systems (notifications, billing, etc.)
                var integrationEvent = new BookingConfirmedIntegrationEvent(
                    domainEvent.BookingId,
                    domainEvent.UserId,
                    domainEvent.VehicleId,
                    "<EMAIL>", // Would get from user repository
                    "Vehicle Info", // Would get from vehicle repository
                    domainEvent.StartTime,
                    domainEvent.EndTime,
                    0m) // Would calculate estimated cost
                {
                    CorrelationId = domainEvent.CorrelationId,
                    CausationId = domainEvent.EventId.ToString()
                };

                await _integrationEventBus.PublishAsync(integrationEvent, cancellationToken);

                _logger.LogInformation("Successfully processed booking confirmation for {BookingId}", domainEvent.BookingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling BookingConfirmedDomainEvent for booking {BookingId}: {ErrorMessage}", 
                    domainEvent.BookingId, ex.Message);
                throw;
            }
        }

        private async Task ReserveVehicleAsync(Guid vehicleId, Guid bookingId, CancellationToken cancellationToken)
        {
            // Simulate vehicle reservation
            await Task.Delay(50, cancellationToken);
            _logger.LogInformation("Vehicle {VehicleId} reserved for booking {BookingId}", vehicleId, bookingId);
        }
    }

    /// <summary>
    /// Handler for booking completion events
    /// </summary>
    public class BookingCompletedDomainEventHandler : IEventHandler<BookingCompletedDomainEvent>
    {
        private readonly IIntegrationEventBus _integrationEventBus;
        private readonly ILogger<BookingCompletedDomainEventHandler> _logger;

        public BookingCompletedDomainEventHandler(
            IIntegrationEventBus integrationEventBus,
            ILogger<BookingCompletedDomainEventHandler> logger)
        {
            _integrationEventBus = integrationEventBus ?? throw new ArgumentNullException(nameof(integrationEventBus));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(BookingCompletedDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling BookingCompletedDomainEvent for booking {BookingId}", domainEvent.BookingId);

            try
            {
                // Business logic for completed bookings:
                // 1. Process payment
                // 2. Send receipt
                // 3. Update vehicle status
                // 4. Publish integration event for analytics and billing

                await ProcessPaymentAsync(domainEvent, cancellationToken);

                // Publish integration event for external systems
                var integrationEvent = new TripCompletedIntegrationEvent(
                    domainEvent.BookingId,
                    domainEvent.UserId,
                    domainEvent.VehicleId,
                    "<EMAIL>", // Would get from user repository
                    DateTime.UtcNow.AddHours(-2), // Would get actual start time
                    domainEvent.ActualEndTime,
                    domainEvent.FinalCost,
                    50, // Would calculate actual distance
                    "Start Location", // Would get from booking
                    domainEvent.ReturnLocation)
                {
                    CorrelationId = domainEvent.CorrelationId,
                    CausationId = domainEvent.EventId.ToString()
                };

                await _integrationEventBus.PublishAsync(integrationEvent, cancellationToken);

                _logger.LogInformation("Successfully processed booking completion for {BookingId}", domainEvent.BookingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling BookingCompletedDomainEvent for booking {BookingId}: {ErrorMessage}", 
                    domainEvent.BookingId, ex.Message);
                throw;
            }
        }

        private async Task ProcessPaymentAsync(BookingCompletedDomainEvent domainEvent, CancellationToken cancellationToken)
        {
            // Simulate payment processing
            await Task.Delay(200, cancellationToken);
            _logger.LogInformation("Payment of {FinalCost:C} processed for booking {BookingId}", 
                domainEvent.FinalCost, domainEvent.BookingId);
        }
    }

    /// <summary>
    /// Handler for booking cancellation events
    /// </summary>
    public class BookingCancelledDomainEventHandler : IEventHandler<BookingCancelledDomainEvent>
    {
        private readonly ILogger<BookingCancelledDomainEventHandler> _logger;

        public BookingCancelledDomainEventHandler(ILogger<BookingCancelledDomainEventHandler> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(BookingCancelledDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling BookingCancelledDomainEvent for booking {BookingId}", domainEvent.BookingId);

            try
            {
                // Business logic for cancelled bookings:
                // 1. Release vehicle reservation
                // 2. Process cancellation fee (if applicable)
                // 3. Send cancellation confirmation
                // 4. Update availability

                await ReleaseVehicleReservationAsync(domainEvent.VehicleId, domainEvent.BookingId, cancellationToken);
                await ProcessCancellationAsync(domainEvent, cancellationToken);

                _logger.LogInformation("Successfully processed booking cancellation for {BookingId} by {CancelledBy}", 
                    domainEvent.BookingId, domainEvent.CancelledBy);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling BookingCancelledDomainEvent for booking {BookingId}: {ErrorMessage}", 
                    domainEvent.BookingId, ex.Message);
                throw;
            }
        }

        private async Task ReleaseVehicleReservationAsync(Guid vehicleId, Guid bookingId, CancellationToken cancellationToken)
        {
            // Simulate releasing vehicle reservation
            await Task.Delay(50, cancellationToken);
            _logger.LogInformation("Vehicle {VehicleId} reservation released for cancelled booking {BookingId}", vehicleId, bookingId);
        }

        private async Task ProcessCancellationAsync(BookingCancelledDomainEvent domainEvent, CancellationToken cancellationToken)
        {
            // Simulate cancellation processing
            await Task.Delay(100, cancellationToken);
            _logger.LogInformation("Cancellation processed for booking {BookingId}, reason: {Reason}", 
                domainEvent.BookingId, domainEvent.Reason);
        }
    }
}
