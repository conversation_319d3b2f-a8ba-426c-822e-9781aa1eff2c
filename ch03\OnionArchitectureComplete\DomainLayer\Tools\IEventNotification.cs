﻿using System;
using System.Collections.Generic;
using System.Text;

namespace DDD.DomainLayer
{
    /// <summary>
    /// Base interface for all event notifications (both domain and integration events)
    /// </summary>
    public interface IEventNotification
    {
        /// <summary>
        /// Unique identifier for the event
        /// </summary>
        Guid EventId { get; }

        /// <summary>
        /// When the event occurred
        /// </summary>
        DateTime OccurredOn { get; }
    }

    /// <summary>
    /// Marker interface for domain events that occur within a bounded context
    /// </summary>
    public interface IDomainEvent : IEventNotification
    {
    }
}
