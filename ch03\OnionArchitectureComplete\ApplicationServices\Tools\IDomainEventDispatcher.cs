using DDD.DomainLayer;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DDD.ApplicationLayer
{
    /// <summary>
    /// Interface for dispatching domain events to their handlers
    /// </summary>
    public interface IDomainEventDispatcher
    {
        /// <summary>
        /// Dispatch a single domain event to its handlers
        /// </summary>
        /// <param name="domainEvent">The domain event to dispatch</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        Task DispatchAsync(IDomainEvent domainEvent, CancellationToken cancellationToken = default);

        /// <summary>
        /// Dispatch multiple domain events to their handlers
        /// </summary>
        /// <param name="domainEvents">The domain events to dispatch</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        Task DispatchAsync(IEnumerable<IDomainEvent> domainEvents, CancellationToken cancellationToken = default);
    }
}
