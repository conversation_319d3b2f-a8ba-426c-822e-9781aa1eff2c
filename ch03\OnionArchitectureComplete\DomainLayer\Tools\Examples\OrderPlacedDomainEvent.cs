using System;
using System.Collections.Generic;
using System.Linq;

namespace DDD.DomainLayer.Examples
{
    /// <summary>
    /// Example domain event that occurs when an order is placed
    /// </summary>
    public class OrderPlacedDomainEvent : DomainEvent
    {
        public OrderPlacedDomainEvent(Guid orderId, Guid customerId, decimal totalAmount, IEnumerable<OrderItem> items)
        {
            OrderId = orderId;
            CustomerId = customerId;
            TotalAmount = totalAmount;
            Items = items?.ToList() ?? throw new ArgumentNullException(nameof(items));
        }

        /// <summary>
        /// The ID of the placed order
        /// </summary>
        public Guid OrderId { get; private set; }

        /// <summary>
        /// The ID of the customer who placed the order
        /// </summary>
        public Guid CustomerId { get; private set; }

        /// <summary>
        /// The total amount of the order
        /// </summary>
        public decimal TotalAmount { get; private set; }

        /// <summary>
        /// The items in the order
        /// </summary>
        public IReadOnlyList<OrderItem> Items { get; private set; }

        public override string ToString()
        {
            return $"OrderPlacedDomainEvent: OrderId={OrderId}, CustomerId={CustomerId}, TotalAmount={TotalAmount:C}, ItemCount={Items.Count}";
        }
    }

    /// <summary>
    /// Represents an item in an order
    /// </summary>
    public class OrderItem
    {
        public OrderItem(Guid productId, string productName, int quantity, decimal unitPrice)
        {
            ProductId = productId;
            ProductName = productName ?? throw new ArgumentNullException(nameof(productName));
            Quantity = quantity;
            UnitPrice = unitPrice;
        }

        public Guid ProductId { get; private set; }
        public string ProductName { get; private set; }
        public int Quantity { get; private set; }
        public decimal UnitPrice { get; private set; }
        public decimal TotalPrice => Quantity * UnitPrice;
    }
}
