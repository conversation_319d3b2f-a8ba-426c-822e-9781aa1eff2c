using System;
using System.Collections.Generic;

namespace DDD.DomainLayer.Examples
{
    /// <summary>
    /// Example integration event that notifies other services when an order is completed
    /// </summary>
    public class OrderCompletedIntegrationEvent : IntegrationEvent
    {
        public OrderCompletedIntegrationEvent(Guid orderId, Guid customerId, decimal totalAmount, string customerEmail, string source = "OrderService")
            : base(source)
        {
            OrderId = orderId;
            CustomerId = customerId;
            TotalAmount = totalAmount;
            CustomerEmail = customerEmail ?? throw new ArgumentNullException(nameof(customerEmail));
            RoutingKey = "order.completed";
            
            // Add some metadata
            Metadata = new Dictionary<string, object>
            {
                { "OrderValue", totalAmount },
                { "Currency", "USD" },
                { "CompletedAt", DateTime.UtcNow }
            };
        }

        /// <summary>
        /// The ID of the completed order
        /// </summary>
        public Guid OrderId { get; private set; }

        /// <summary>
        /// The ID of the customer
        /// </summary>
        public Guid CustomerId { get; private set; }

        /// <summary>
        /// The total amount of the order
        /// </summary>
        public decimal TotalAmount { get; private set; }

        /// <summary>
        /// The email of the customer for notification purposes
        /// </summary>
        public string CustomerEmail { get; private set; }

        public override string ToString()
        {
            return $"OrderCompletedIntegrationEvent: OrderId={OrderId}, CustomerId={CustomerId}, TotalAmount={TotalAmount:C}, CustomerEmail={CustomerEmail}, Source={Source}";
        }
    }
}
