using DDD.DomainLayer;
using DDD.DomainLayer.Examples;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace DDD.ApplicationLayer.Examples
{
    /// <summary>
    /// Example handler for OrderPlacedDomainEvent that performs business logic
    /// </summary>
    public class OrderPlacedDomainEventHandler : IEventHandler<OrderPlacedDomainEvent>
    {
        private readonly ILogger<OrderPlacedDomainEventHandler> _logger;

        public OrderPlacedDomainEventHandler(ILogger<OrderPlacedDomainEventHandler> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(OrderPlacedDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling OrderPlacedDomainEvent for order {OrderId}", domainEvent.OrderId);

            try
            {
                // Example business logic that could be performed:
                // 1. Update inventory
                // 2. Send confirmation email
                // 3. Create shipping request
                // 4. Update customer loyalty points

                // Simulate some async work
                await Task.Delay(100, cancellationToken);

                // Log the order details
                _logger.LogInformation("Order {OrderId} placed by customer {CustomerId} for {TotalAmount:C} with {ItemCount} items",
                    domainEvent.OrderId,
                    domainEvent.CustomerId,
                    domainEvent.TotalAmount,
                    domainEvent.Items.Count);

                // In a real scenario, you might:
                // - Update inventory levels
                // - Send notifications
                // - Trigger other business processes

                _logger.LogInformation("Successfully processed OrderPlacedDomainEvent for order {OrderId}", domainEvent.OrderId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling OrderPlacedDomainEvent for order {OrderId}: {ErrorMessage}", 
                    domainEvent.OrderId, ex.Message);
                throw;
            }
        }
    }
}
