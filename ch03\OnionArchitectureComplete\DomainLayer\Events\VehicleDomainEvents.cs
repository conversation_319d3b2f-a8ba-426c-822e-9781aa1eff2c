using System;
using DDD.DomainLayer.Entities;

namespace DDD.DomainLayer.Events
{
    /// <summary>
    /// Domain event raised when a vehicle is added to the fleet
    /// </summary>
    public class VehicleAddedToFleetDomainEvent : DomainEvent
    {
        public VehicleAddedToFleetDomainEvent(Guid vehicleId, string make, string model, string licensePlate, VehicleType vehicleType)
        {
            VehicleId = vehicleId;
            Make = make ?? throw new ArgumentNullException(nameof(make));
            Model = model ?? throw new ArgumentNullException(nameof(model));
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            VehicleType = vehicleType;
        }

        public Guid VehicleId { get; private set; }
        public string Make { get; private set; }
        public string Model { get; private set; }
        public string LicensePlate { get; private set; }
        public VehicleType VehicleType { get; private set; }

        public override string ToString()
        {
            return $"VehicleAddedToFleetDomainEvent: VehicleId={VehicleId}, Vehicle={Make} {Model} ({LicensePlate}), Type={VehicleType}";
        }
    }

    /// <summary>
    /// Domain event raised when a vehicle is reserved for a booking
    /// </summary>
    public class VehicleReservedDomainEvent : DomainEvent
    {
        public VehicleReservedDomainEvent(Guid vehicleId, string licensePlate, Guid bookingId)
        {
            VehicleId = vehicleId;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            BookingId = bookingId;
        }

        public Guid VehicleId { get; private set; }
        public string LicensePlate { get; private set; }
        public Guid BookingId { get; private set; }

        public override string ToString()
        {
            return $"VehicleReservedDomainEvent: VehicleId={VehicleId}, LicensePlate={LicensePlate}, BookingId={BookingId}";
        }
    }

    /// <summary>
    /// Domain event raised when a trip is started with a vehicle
    /// </summary>
    public class VehicleTripStartedDomainEvent : DomainEvent
    {
        public VehicleTripStartedDomainEvent(Guid vehicleId, string licensePlate, Guid tripId, Guid userId)
        {
            VehicleId = vehicleId;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            TripId = tripId;
            UserId = userId;
        }

        public Guid VehicleId { get; private set; }
        public string LicensePlate { get; private set; }
        public Guid TripId { get; private set; }
        public Guid UserId { get; private set; }

        public override string ToString()
        {
            return $"VehicleTripStartedDomainEvent: VehicleId={VehicleId}, LicensePlate={LicensePlate}, TripId={TripId}, UserId={UserId}";
        }
    }

    /// <summary>
    /// Domain event raised when a trip ends and vehicle is returned
    /// </summary>
    public class VehicleTripEndedDomainEvent : DomainEvent
    {
        public VehicleTripEndedDomainEvent(Guid vehicleId, string licensePlate, Guid tripId, string endLocation, int distanceTraveled, int finalFuelLevel)
        {
            VehicleId = vehicleId;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            TripId = tripId;
            EndLocation = endLocation ?? throw new ArgumentNullException(nameof(endLocation));
            DistanceTraveled = distanceTraveled;
            FinalFuelLevel = finalFuelLevel;
        }

        public Guid VehicleId { get; private set; }
        public string LicensePlate { get; private set; }
        public Guid TripId { get; private set; }
        public string EndLocation { get; private set; }
        public int DistanceTraveled { get; private set; }
        public int FinalFuelLevel { get; private set; }

        public override string ToString()
        {
            return $"VehicleTripEndedDomainEvent: VehicleId={VehicleId}, LicensePlate={LicensePlate}, TripId={TripId}, EndLocation={EndLocation}, Distance={DistanceTraveled}km, Fuel={FinalFuelLevel}%";
        }
    }

    /// <summary>
    /// Domain event raised when vehicle maintenance is started
    /// </summary>
    public class VehicleMaintenanceStartedDomainEvent : DomainEvent
    {
        public VehicleMaintenanceStartedDomainEvent(Guid vehicleId, string licensePlate, string reason)
        {
            VehicleId = vehicleId;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            Reason = reason ?? throw new ArgumentNullException(nameof(reason));
        }

        public Guid VehicleId { get; private set; }
        public string LicensePlate { get; private set; }
        public string Reason { get; private set; }

        public override string ToString()
        {
            return $"VehicleMaintenanceStartedDomainEvent: VehicleId={VehicleId}, LicensePlate={LicensePlate}, Reason={Reason}";
        }
    }

    /// <summary>
    /// Domain event raised when vehicle maintenance is completed
    /// </summary>
    public class VehicleMaintenanceCompletedDomainEvent : DomainEvent
    {
        public VehicleMaintenanceCompletedDomainEvent(Guid vehicleId, string licensePlate)
        {
            VehicleId = vehicleId;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
        }

        public Guid VehicleId { get; private set; }
        public string LicensePlate { get; private set; }

        public override string ToString()
        {
            return $"VehicleMaintenanceCompletedDomainEvent: VehicleId={VehicleId}, LicensePlate={LicensePlate}";
        }
    }

    /// <summary>
    /// Domain event raised when a vehicle is removed from the fleet
    /// </summary>
    public class VehicleRemovedFromFleetDomainEvent : DomainEvent
    {
        public VehicleRemovedFromFleetDomainEvent(Guid vehicleId, string licensePlate, string reason)
        {
            VehicleId = vehicleId;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            Reason = reason ?? throw new ArgumentNullException(nameof(reason));
        }

        public Guid VehicleId { get; private set; }
        public string LicensePlate { get; private set; }
        public string Reason { get; private set; }

        public override string ToString()
        {
            return $"VehicleRemovedFromFleetDomainEvent: VehicleId={VehicleId}, LicensePlate={LicensePlate}, Reason={Reason}";
        }
    }

    /// <summary>
    /// Domain event raised when vehicle location is updated
    /// </summary>
    public class VehicleLocationUpdatedDomainEvent : DomainEvent
    {
        public VehicleLocationUpdatedDomainEvent(Guid vehicleId, string licensePlate, string oldLocation, string newLocation)
        {
            VehicleId = vehicleId;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            OldLocation = oldLocation ?? throw new ArgumentNullException(nameof(oldLocation));
            NewLocation = newLocation ?? throw new ArgumentNullException(nameof(newLocation));
        }

        public Guid VehicleId { get; private set; }
        public string LicensePlate { get; private set; }
        public string OldLocation { get; private set; }
        public string NewLocation { get; private set; }

        public override string ToString()
        {
            return $"VehicleLocationUpdatedDomainEvent: VehicleId={VehicleId}, LicensePlate={LicensePlate}, Location: {OldLocation} -> {NewLocation}";
        }
    }
}
