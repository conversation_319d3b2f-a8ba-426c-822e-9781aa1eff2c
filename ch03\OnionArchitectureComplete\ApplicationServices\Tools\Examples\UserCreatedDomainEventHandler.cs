using DDD.DomainLayer;
using DDD.DomainLayer.Examples;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace DDD.ApplicationLayer.Examples
{
    /// <summary>
    /// Example handler for UserCreatedDomainEvent that publishes an integration event
    /// </summary>
    public class UserCreatedDomainEventHandler : IEventHandler<UserCreatedDomainEvent>
    {
        private readonly IIntegrationEventBus _integrationEventBus;
        private readonly ILogger<UserCreatedDomainEventHandler> _logger;

        public UserCreatedDomainEventHandler(
            IIntegrationEventBus integrationEventBus,
            ILogger<UserCreatedDomainEventHandler> logger)
        {
            _integrationEventBus = integrationEventBus ?? throw new ArgumentNullException(nameof(integrationEventBus));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(UserCreatedDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling UserCreatedDomainEvent for user {UserId}", domainEvent.UserId);

            try
            {
                // Create and publish integration event to notify other services
                var integrationEvent = new UserRegisteredIntegrationEvent(
                    domainEvent.UserId,
                    domainEvent.Email,
                    domainEvent.FirstName,
                    domainEvent.LastName)
                {
                    CorrelationId = domainEvent.CorrelationId,
                    CausationId = domainEvent.EventId.ToString()
                };

                await _integrationEventBus.PublishAsync(integrationEvent, cancellationToken);

                _logger.LogInformation("Successfully published UserRegisteredIntegrationEvent for user {UserId}", domainEvent.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling UserCreatedDomainEvent for user {UserId}: {ErrorMessage}", 
                    domainEvent.UserId, ex.Message);
                throw;
            }
        }
    }
}
