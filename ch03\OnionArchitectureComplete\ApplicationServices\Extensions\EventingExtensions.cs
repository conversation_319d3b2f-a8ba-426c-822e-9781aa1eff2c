using DDD.ApplicationLayer;
using DDD.ApplicationLayer.Examples;
using DDD.DomainLayer;
using DDD.DomainLayer.Examples;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace DDD.ApplicationLayer.Extensions
{
    /// <summary>
    /// Extension methods for registering eventing services
    /// </summary>
    public static class EventingExtensions
    {
        /// <summary>
        /// Add domain and integration event services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddEventing(this IServiceCollection services)
        {
            // Register domain event dispatcher
            services.AddScoped<IDomainEventDispatcher, DomainEventDispatcher>();

            // Register integration event bus (in-memory implementation for demo)
            services.AddSingleton<IIntegrationEventBus, InMemoryIntegrationEventBus>();

            // Register domain event handlers
            services.AddScoped<IEventHandler<UserCreatedDomainEvent>, UserCreatedDomainEventHandler>();
            services.AddScoped<IEventHandler<OrderPlacedDomainEvent>, OrderPlacedDomainEventHandler>();

            // Register the legacy event mediator for backward compatibility
            services.AddScoped<EventMediator>();

            return services;
        }

        /// <summary>
        /// Add domain event handlers for a specific assembly
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="assembly">The assembly to scan for event handlers</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddDomainEventHandlers(this IServiceCollection services, System.Reflection.Assembly assembly)
        {
            var handlerTypes = assembly.GetTypes()
                .Where(t => t.GetInterfaces()
                    .Any(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IEventHandler<>)))
                .Where(t => !t.IsAbstract && !t.IsInterface);

            foreach (var handlerType in handlerTypes)
            {
                var interfaceTypes = handlerType.GetInterfaces()
                    .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IEventHandler<>));

                foreach (var interfaceType in interfaceTypes)
                {
                    services.AddScoped(interfaceType, handlerType);
                }
            }

            return services;
        }
    }
}
