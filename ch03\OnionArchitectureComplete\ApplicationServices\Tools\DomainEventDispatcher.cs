using DDD.DomainLayer;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DDD.ApplicationLayer
{
    /// <summary>
    /// Implementation of domain event dispatcher using the service provider
    /// </summary>
    public class DomainEventDispatcher : IDomainEventDispatcher
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DomainEventDispatcher> _logger;

        public DomainEventDispatcher(IServiceProvider serviceProvider, ILogger<DomainEventDispatcher> logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task DispatchAsync(IDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            if (domainEvent == null)
                throw new ArgumentNullException(nameof(domainEvent));

            _logger.LogDebug("Dispatching domain event {EventType} with ID {EventId}", 
                domainEvent.GetType().Name, domainEvent.EventId);

            var handlerType = typeof(IEventHandler<>).MakeGenericType(domainEvent.GetType());
            var handlers = _serviceProvider.GetServices(handlerType);

            var tasks = handlers.Select(async handler =>
            {
                try
                {
                    _logger.LogDebug("Executing handler {HandlerType} for event {EventType}", 
                        handler.GetType().Name, domainEvent.GetType().Name);

                    var method = handlerType.GetMethod(nameof(IEventHandler<IDomainEvent>.Handle));
                    var task = (Task)method!.Invoke(handler, new object[] { domainEvent, cancellationToken })!;
                    await task;

                    _logger.LogDebug("Successfully executed handler {HandlerType} for event {EventType}", 
                        handler.GetType().Name, domainEvent.GetType().Name);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error executing handler {HandlerType} for event {EventType}: {ErrorMessage}", 
                        handler.GetType().Name, domainEvent.GetType().Name, ex.Message);
                    throw;
                }
            });

            await Task.WhenAll(tasks);

            _logger.LogDebug("Completed dispatching domain event {EventType} with ID {EventId}", 
                domainEvent.GetType().Name, domainEvent.EventId);
        }

        public async Task DispatchAsync(IEnumerable<IDomainEvent> domainEvents, CancellationToken cancellationToken = default)
        {
            if (domainEvents == null)
                throw new ArgumentNullException(nameof(domainEvents));

            var events = domainEvents.ToList();
            
            _logger.LogDebug("Dispatching {EventCount} domain events", events.Count);

            foreach (var domainEvent in events)
            {
                await DispatchAsync(domainEvent, cancellationToken);
            }

            _logger.LogDebug("Completed dispatching {EventCount} domain events", events.Count);
        }
    }
}
