# Domain Events, Integration Events, and Unit of Work Patterns

This document explains the enhanced eventing patterns implemented in this Onion Architecture example.

## Overview

This implementation demonstrates three key patterns:

1. **Domain Events** - Events that occur within a bounded context
2. **Integration Events** - Events that cross bounded contexts or microservices
3. **Enhanced Unit of Work** - Coordinating transactions with event dispatching

## Domain Events

Domain events represent something important that happened in the domain. They are used for decoupling business logic within the same bounded context.

### Key Components

- `IDomainEvent` - Marker interface for domain events
- `DomainEvent` - Base class with common properties (EventId, OccurredOn, etc.)
- `IEventHandler<T>` - Interface for domain event handlers
- `IDomainEventDispatcher` - Service for dispatching domain events

### Example Usage

```csharp
// Domain Event
public class UserCreatedDomainEvent : DomainEvent
{
    public Guid UserId { get; private set; }
    public string Email { get; private set; }
    // ... other properties
}

// Entity raising the event
public class User : Entity<Guid>
{
    public User(string email, string firstName, string lastName)
    {
        // ... initialization
        AddDomainEvent(new UserCreatedDomainEvent(Id, Email, FirstName, LastName));
    }
}

// Event Handler
public class UserCreatedDomainEventHandler : IEventHandler<UserCreatedDomainEvent>
{
    public async Task Handle(UserCreatedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        // Handle the event (e.g., send welcome email, create profile, etc.)
    }
}
```

## Integration Events

Integration events are used for communication between different microservices or bounded contexts. They are typically published to a message broker.

### Key Components

- `IIntegrationEvent` - Interface for integration events
- `IntegrationEvent` - Base class with routing and metadata support
- `IIntegrationEventBus` - Service for publishing integration events
- `InMemoryIntegrationEventBus` - In-memory implementation for demonstration

### Example Usage

```csharp
// Integration Event
public class UserRegisteredIntegrationEvent : IntegrationEvent
{
    public UserRegisteredIntegrationEvent(Guid userId, string email, string source = "UserService")
        : base(source)
    {
        UserId = userId;
        Email = email;
        RoutingKey = "user.registered";
    }
}

// Publishing integration events
await _integrationEventBus.PublishAsync(integrationEvent, cancellationToken);
```

## Enhanced Unit of Work

The Unit of Work pattern has been enhanced to automatically dispatch domain events when saving changes.

### Key Features

- Automatic domain event collection from entities
- Event dispatching after successful database save
- Transaction management with proper rollback
- Integration with Entity Framework Core

### Usage

```csharp
// The UnitOfWork automatically:
// 1. Collects domain events from tracked entities
// 2. Saves changes to the database
// 3. Dispatches domain events if save was successful
// 4. Clears domain events from entities

var success = await _unitOfWork.SaveEntitiesAsync(cancellationToken);
```

## Event Flow

1. **Entity Operation** - Business operation on an entity
2. **Domain Event Added** - Entity adds domain event to its collection
3. **Save Changes** - UnitOfWork.SaveEntitiesAsync() is called
4. **Database Save** - Changes are persisted to database
5. **Event Dispatch** - Domain events are dispatched to handlers
6. **Integration Events** - Handlers may publish integration events
7. **Event Cleanup** - Domain events are cleared from entities

## Configuration

Register the eventing services in your DI container:

```csharp
services.AddEventing(); // Registers all eventing services
services.AddDomainEventHandlers(Assembly.GetExecutingAssembly()); // Auto-register handlers
```

## Best Practices

1. **Domain Events** should be used for business logic within the same bounded context
2. **Integration Events** should be used for cross-service communication
3. **Event Handlers** should be idempotent and handle failures gracefully
4. **Domain Events** are dispatched synchronously within the same transaction
5. **Integration Events** should be published after the transaction commits
6. Use **correlation IDs** to track related events across services
7. Version your events for backward compatibility

## Files Structure

```
DomainLayer/Tools/
├── IEventNotification.cs          # Base event interfaces
├── DomainEvent.cs                 # Base domain event class
├── IIntegrationEvent.cs           # Integration event interface
├── IntegrationEvent.cs            # Base integration event class
├── IIntegrationEventBus.cs        # Integration event bus interface
├── IUnitOfWork.cs                 # Enhanced UnitOfWork interface
└── Examples/                      # Example events and entities
    ├── UserCreatedDomainEvent.cs
    ├── OrderPlacedDomainEvent.cs
    ├── UserRegisteredIntegrationEvent.cs
    ├── OrderCompletedIntegrationEvent.cs
    └── User.cs

ApplicationServices/Tools/
├── IDomainEventDispatcher.cs      # Domain event dispatcher interface
├── DomainEventDispatcher.cs       # Domain event dispatcher implementation
├── InMemoryIntegrationEventBus.cs # In-memory integration event bus
└── Examples/                      # Example event handlers
    ├── UserCreatedDomainEventHandler.cs
    └── OrderPlacedDomainEventHandler.cs

DBDriver/
└── MainDbContext.cs               # Enhanced with event dispatching
```

This implementation provides a solid foundation for event-driven architecture within the Onion Architecture pattern.
