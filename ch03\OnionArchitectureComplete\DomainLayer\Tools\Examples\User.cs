using System;

namespace DDD.DomainLayer.Examples
{
    /// <summary>
    /// Example User entity that demonstrates domain events usage
    /// </summary>
    public class User : Entity<Guid>
    {
        private User() { } // For EF Core

        public User(string email, string firstName, string lastName)
        {
            Id = Guid.NewGuid();
            Email = email ?? throw new ArgumentNullException(nameof(email));
            FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
            LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
            CreatedAt = DateTime.UtcNow;
            IsActive = true;

            // Raise domain event when user is created
            AddDomainEvent(new UserCreatedDomainEvent(Id, Email, FirstName, LastName));
        }

        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; private set; } = string.Empty;

        /// <summary>
        /// User's first name
        /// </summary>
        public string FirstName { get; private set; } = string.Empty;

        /// <summary>
        /// User's last name
        /// </summary>
        public string LastName { get; private set; } = string.Empty;

        /// <summary>
        /// When the user was created
        /// </summary>
        public DateTime CreatedAt { get; private set; }

        /// <summary>
        /// Whether the user is active
        /// </summary>
        public bool IsActive { get; private set; }

        /// <summary>
        /// User's full name
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Update user's email
        /// </summary>
        /// <param name="newEmail">The new email address</param>
        public void UpdateEmail(string newEmail)
        {
            if (string.IsNullOrWhiteSpace(newEmail))
                throw new ArgumentException("Email cannot be empty", nameof(newEmail));

            if (Email != newEmail)
            {
                var oldEmail = Email;
                Email = newEmail;

                // You could raise a domain event here for email changes
                // AddDomainEvent(new UserEmailChangedDomainEvent(Id, oldEmail, newEmail));
            }
        }

        /// <summary>
        /// Deactivate the user
        /// </summary>
        public void Deactivate()
        {
            if (IsActive)
            {
                IsActive = false;
                
                // You could raise a domain event here
                // AddDomainEvent(new UserDeactivatedDomainEvent(Id, Email));
            }
        }

        /// <summary>
        /// Activate the user
        /// </summary>
        public void Activate()
        {
            if (!IsActive)
            {
                IsActive = true;
                
                // You could raise a domain event here
                // AddDomainEvent(new UserActivatedDomainEvent(Id, Email));
            }
        }

        public override string ToString()
        {
            return $"User: {Id} - {FullName} ({Email}) - Active: {IsActive}";
        }
    }
}
