using System;
using DDD.DomainLayer.Events;

namespace DDD.DomainLayer.Entities
{
    /// <summary>
    /// Vehicle entity for the car sharing platform
    /// </summary>
    public class Vehicle : Entity<Guid>
    {
        private Vehicle() { } // For EF Core

        public Vehicle(string make, string model, int year, string licensePlate, string vin, 
                      VehicleType vehicleType, decimal hourlyRate, string location)
        {
            Id = Guid.NewGuid();
            Make = make ?? throw new ArgumentNullException(nameof(make));
            Model = model ?? throw new ArgumentNullException(nameof(model));
            Year = year;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            VIN = vin ?? throw new ArgumentNullException(nameof(vin));
            VehicleType = vehicleType;
            HourlyRate = hourlyRate;
            CurrentLocation = location ?? throw new ArgumentNullException(nameof(location));
            Status = VehicleStatus.Available;
            CreatedAt = DateTime.UtcNow;
            FuelLevel = 100; // Start with full tank
            BatteryLevel = 100; // For electric vehicles

            // Raise domain event when vehicle is added to fleet
            AddDomainEvent(new VehicleAddedToFleetDomainEvent(Id, Make, Model, LicensePlate, VehicleType));
        }

        /// <summary>
        /// Vehicle make (e.g., Toyota, BMW)
        /// </summary>
        public string Make { get; private set; } = string.Empty;

        /// <summary>
        /// Vehicle model (e.g., Camry, X3)
        /// </summary>
        public string Model { get; private set; } = string.Empty;

        /// <summary>
        /// Manufacturing year
        /// </summary>
        public int Year { get; private set; }

        /// <summary>
        /// License plate number
        /// </summary>
        public string LicensePlate { get; private set; } = string.Empty;

        /// <summary>
        /// Vehicle Identification Number
        /// </summary>
        public string VIN { get; private set; } = string.Empty;

        /// <summary>
        /// Type of vehicle (Sedan, SUV, Electric, etc.)
        /// </summary>
        public VehicleType VehicleType { get; private set; }

        /// <summary>
        /// Hourly rental rate
        /// </summary>
        public decimal HourlyRate { get; private set; }

        /// <summary>
        /// Current location of the vehicle
        /// </summary>
        public string CurrentLocation { get; private set; } = string.Empty;

        /// <summary>
        /// Current status of the vehicle
        /// </summary>
        public VehicleStatus Status { get; private set; }

        /// <summary>
        /// Current fuel level (0-100%)
        /// </summary>
        public int FuelLevel { get; private set; }

        /// <summary>
        /// Current battery level for electric vehicles (0-100%)
        /// </summary>
        public int BatteryLevel { get; private set; }

        /// <summary>
        /// When the vehicle was added to the fleet
        /// </summary>
        public DateTime CreatedAt { get; private set; }

        /// <summary>
        /// Last maintenance date
        /// </summary>
        public DateTime? LastMaintenanceDate { get; private set; }

        /// <summary>
        /// Total mileage
        /// </summary>
        public int Mileage { get; private set; }

        /// <summary>
        /// Vehicle display name
        /// </summary>
        public string DisplayName => $"{Year} {Make} {Model}";

        /// <summary>
        /// Reserve the vehicle for a booking
        /// </summary>
        /// <param name="bookingId">The booking ID</param>
        public void Reserve(Guid bookingId)
        {
            if (Status != VehicleStatus.Available)
                throw new InvalidOperationException($"Vehicle {LicensePlate} is not available for reservation");

            Status = VehicleStatus.Reserved;
            
            AddDomainEvent(new VehicleReservedDomainEvent(Id, LicensePlate, bookingId));
        }

        /// <summary>
        /// Start a trip with this vehicle
        /// </summary>
        /// <param name="tripId">The trip ID</param>
        /// <param name="userId">The user starting the trip</param>
        public void StartTrip(Guid tripId, Guid userId)
        {
            if (Status != VehicleStatus.Reserved)
                throw new InvalidOperationException($"Vehicle {LicensePlate} must be reserved before starting a trip");

            Status = VehicleStatus.InUse;
            
            AddDomainEvent(new VehicleTripStartedDomainEvent(Id, LicensePlate, tripId, userId));
        }

        /// <summary>
        /// End a trip and make vehicle available
        /// </summary>
        /// <param name="tripId">The trip ID</param>
        /// <param name="endLocation">Where the trip ended</param>
        /// <param name="finalMileage">Final mileage reading</param>
        /// <param name="finalFuelLevel">Final fuel level</param>
        public void EndTrip(Guid tripId, string endLocation, int finalMileage, int finalFuelLevel)
        {
            if (Status != VehicleStatus.InUse)
                throw new InvalidOperationException($"Vehicle {LicensePlate} is not currently in use");

            var previousMileage = Mileage;
            var distanceTraveled = finalMileage - previousMileage;

            Status = VehicleStatus.Available;
            CurrentLocation = endLocation;
            Mileage = finalMileage;
            FuelLevel = finalFuelLevel;
            
            AddDomainEvent(new VehicleTripEndedDomainEvent(Id, LicensePlate, tripId, endLocation, distanceTraveled, finalFuelLevel));
        }

        /// <summary>
        /// Put vehicle into maintenance
        /// </summary>
        /// <param name="reason">Reason for maintenance</param>
        public void StartMaintenance(string reason)
        {
            if (Status == VehicleStatus.InUse)
                throw new InvalidOperationException($"Cannot start maintenance on vehicle {LicensePlate} while in use");

            Status = VehicleStatus.Maintenance;
            
            AddDomainEvent(new VehicleMaintenanceStartedDomainEvent(Id, LicensePlate, reason));
        }

        /// <summary>
        /// Complete maintenance and make vehicle available
        /// </summary>
        public void CompleteMaintenance()
        {
            if (Status != VehicleStatus.Maintenance)
                throw new InvalidOperationException($"Vehicle {LicensePlate} is not in maintenance");

            Status = VehicleStatus.Available;
            LastMaintenanceDate = DateTime.UtcNow;
            
            AddDomainEvent(new VehicleMaintenanceCompletedDomainEvent(Id, LicensePlate));
        }

        /// <summary>
        /// Remove vehicle from fleet
        /// </summary>
        /// <param name="reason">Reason for removal</param>
        public void RemoveFromFleet(string reason)
        {
            if (Status == VehicleStatus.InUse)
                throw new InvalidOperationException($"Cannot remove vehicle {LicensePlate} while in use");

            Status = VehicleStatus.OutOfService;
            
            AddDomainEvent(new VehicleRemovedFromFleetDomainEvent(Id, LicensePlate, reason));
        }

        /// <summary>
        /// Update vehicle location
        /// </summary>
        /// <param name="newLocation">New location</param>
        public void UpdateLocation(string newLocation)
        {
            if (string.IsNullOrWhiteSpace(newLocation))
                throw new ArgumentException("Location cannot be empty", nameof(newLocation));

            var oldLocation = CurrentLocation;
            CurrentLocation = newLocation;
            
            AddDomainEvent(new VehicleLocationUpdatedDomainEvent(Id, LicensePlate, oldLocation, newLocation));
        }

        public override string ToString()
        {
            return $"Vehicle: {DisplayName} ({LicensePlate}) - Status: {Status}, Location: {CurrentLocation}";
        }
    }

    /// <summary>
    /// Vehicle type enumeration
    /// </summary>
    public enum VehicleType
    {
        Sedan,
        SUV,
        Hatchback,
        Electric,
        Hybrid,
        Luxury,
        Van
    }

    /// <summary>
    /// Vehicle status enumeration
    /// </summary>
    public enum VehicleStatus
    {
        Available,
        Reserved,
        InUse,
        Maintenance,
        OutOfService
    }
}
