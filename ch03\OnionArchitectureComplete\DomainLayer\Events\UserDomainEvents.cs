using System;

namespace DDD.DomainLayer.Events
{
    /// <summary>
    /// Domain event raised when a user registers on the platform
    /// </summary>
    public class UserRegisteredDomainEvent : DomainEvent
    {
        public UserRegisteredDomainEvent(Guid userId, string email, string firstName, string lastName, string phoneNumber)
        {
            UserId = userId;
            Email = email ?? throw new ArgumentNullException(nameof(email));
            FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
            LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
            PhoneNumber = phoneNumber ?? throw new ArgumentNullException(nameof(phoneNumber));
        }

        public Guid UserId { get; private set; }
        public string Email { get; private set; }
        public string FirstName { get; private set; }
        public string LastName { get; private set; }
        public string PhoneNumber { get; private set; }

        public override string ToString()
        {
            return $"UserRegisteredDomainEvent: UserId={UserId}, Email={Email}, Name={FirstName} {LastName}";
        }
    }

    /// <summary>
    /// Domain event raised when a user's identity and driving license are verified
    /// </summary>
    public class UserVerifiedDomainEvent : DomainEvent
    {
        public UserVerifiedDomainEvent(Guid userId, string email, string fullName)
        {
            UserId = userId;
            Email = email ?? throw new ArgumentNullException(nameof(email));
            FullName = fullName ?? throw new ArgumentNullException(nameof(fullName));
        }

        public Guid UserId { get; private set; }
        public string Email { get; private set; }
        public string FullName { get; private set; }

        public override string ToString()
        {
            return $"UserVerifiedDomainEvent: UserId={UserId}, Email={Email}, Name={FullName}";
        }
    }

    /// <summary>
    /// Domain event raised when user verification is rejected
    /// </summary>
    public class UserVerificationRejectedDomainEvent : DomainEvent
    {
        public UserVerificationRejectedDomainEvent(Guid userId, string email, string reason)
        {
            UserId = userId;
            Email = email ?? throw new ArgumentNullException(nameof(email));
            Reason = reason ?? throw new ArgumentNullException(nameof(reason));
        }

        public Guid UserId { get; private set; }
        public string Email { get; private set; }
        public string Reason { get; private set; }

        public override string ToString()
        {
            return $"UserVerificationRejectedDomainEvent: UserId={UserId}, Email={Email}, Reason={Reason}";
        }
    }

    /// <summary>
    /// Domain event raised when a user account is deactivated
    /// </summary>
    public class UserAccountDeactivatedDomainEvent : DomainEvent
    {
        public UserAccountDeactivatedDomainEvent(Guid userId, string email, string reason)
        {
            UserId = userId;
            Email = email ?? throw new ArgumentNullException(nameof(email));
            Reason = reason ?? throw new ArgumentNullException(nameof(reason));
        }

        public Guid UserId { get; private set; }
        public string Email { get; private set; }
        public string Reason { get; private set; }

        public override string ToString()
        {
            return $"UserAccountDeactivatedDomainEvent: UserId={UserId}, Email={Email}, Reason={Reason}";
        }
    }

    /// <summary>
    /// Domain event raised when a user account is activated
    /// </summary>
    public class UserAccountActivatedDomainEvent : DomainEvent
    {
        public UserAccountActivatedDomainEvent(Guid userId, string email)
        {
            UserId = userId;
            Email = email ?? throw new ArgumentNullException(nameof(email));
        }

        public Guid UserId { get; private set; }
        public string Email { get; private set; }

        public override string ToString()
        {
            return $"UserAccountActivatedDomainEvent: UserId={UserId}, Email={Email}";
        }
    }

    /// <summary>
    /// Domain event raised when user contact information is updated
    /// </summary>
    public class UserContactInfoUpdatedDomainEvent : DomainEvent
    {
        public UserContactInfoUpdatedDomainEvent(Guid userId, string oldEmail, string newEmail, string oldPhone, string newPhone)
        {
            UserId = userId;
            OldEmail = oldEmail ?? throw new ArgumentNullException(nameof(oldEmail));
            NewEmail = newEmail ?? throw new ArgumentNullException(nameof(newEmail));
            OldPhoneNumber = oldPhone ?? throw new ArgumentNullException(nameof(oldPhone));
            NewPhoneNumber = newPhone ?? throw new ArgumentNullException(nameof(newPhone));
        }

        public Guid UserId { get; private set; }
        public string OldEmail { get; private set; }
        public string NewEmail { get; private set; }
        public string OldPhoneNumber { get; private set; }
        public string NewPhoneNumber { get; private set; }

        public override string ToString()
        {
            return $"UserContactInfoUpdatedDomainEvent: UserId={UserId}, Email: {OldEmail} -> {NewEmail}, Phone: {OldPhoneNumber} -> {NewPhoneNumber}";
        }
    }
}
