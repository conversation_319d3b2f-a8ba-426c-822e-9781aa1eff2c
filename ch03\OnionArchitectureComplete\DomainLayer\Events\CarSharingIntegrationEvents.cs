using System;
using System.Collections.Generic;

namespace DDD.DomainLayer.Events
{
    /// <summary>
    /// Integration event published when a user completes registration and verification
    /// </summary>
    public class UserVerifiedIntegrationEvent : IntegrationEvent
    {
        public UserVerifiedIntegrationEvent(Guid userId, string email, string fullName, string phoneNumber, string source = "CarSharingService")
            : base(source)
        {
            UserId = userId;
            Email = email ?? throw new ArgumentNullException(nameof(email));
            FullName = fullName ?? throw new ArgumentNullException(nameof(fullName));
            PhoneNumber = phoneNumber ?? throw new ArgumentNullException(nameof(phoneNumber));
            RoutingKey = "user.verified";
        }

        public Guid UserId { get; private set; }
        public string Email { get; private set; }
        public string FullName { get; private set; }
        public string PhoneNumber { get; private set; }

        public override string ToString()
        {
            return $"UserVerifiedIntegrationEvent: UserId={UserId}, Email={Email}, Name={FullName}";
        }
    }

    /// <summary>
    /// Integration event published when a booking is confirmed
    /// </summary>
    public class BookingConfirmedIntegrationEvent : IntegrationEvent
    {
        public BookingConfirmedIntegrationEvent(Guid bookingId, Guid userId, Guid vehicleId, string userEmail, 
                                              string vehicleInfo, DateTime startTime, DateTime endTime, decimal estimatedCost, 
                                              string source = "CarSharingService")
            : base(source)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            UserEmail = userEmail ?? throw new ArgumentNullException(nameof(userEmail));
            VehicleInfo = vehicleInfo ?? throw new ArgumentNullException(nameof(vehicleInfo));
            StartTime = startTime;
            EndTime = endTime;
            EstimatedCost = estimatedCost;
            RoutingKey = "booking.confirmed";

            // Add metadata for external systems
            Metadata = new Dictionary<string, object>
            {
                { "BookingDuration", (endTime - startTime).TotalHours },
                { "Currency", "USD" },
                { "BookingType", "CarSharing" }
            };
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public string UserEmail { get; private set; }
        public string VehicleInfo { get; private set; }
        public DateTime StartTime { get; private set; }
        public DateTime EndTime { get; private set; }
        public decimal EstimatedCost { get; private set; }

        public override string ToString()
        {
            return $"BookingConfirmedIntegrationEvent: BookingId={BookingId}, UserId={UserId}, VehicleInfo={VehicleInfo}, StartTime={StartTime}";
        }
    }

    /// <summary>
    /// Integration event published when a trip is completed
    /// </summary>
    public class TripCompletedIntegrationEvent : IntegrationEvent
    {
        public TripCompletedIntegrationEvent(Guid bookingId, Guid userId, Guid vehicleId, string userEmail,
                                           DateTime startTime, DateTime endTime, decimal finalCost, int distanceTraveled,
                                           string startLocation, string endLocation, string source = "CarSharingService")
            : base(source)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            UserEmail = userEmail ?? throw new ArgumentNullException(nameof(userEmail));
            StartTime = startTime;
            EndTime = endTime;
            FinalCost = finalCost;
            DistanceTraveled = distanceTraveled;
            StartLocation = startLocation ?? throw new ArgumentNullException(nameof(startLocation));
            EndLocation = endLocation ?? throw new ArgumentNullException(nameof(endLocation));
            RoutingKey = "trip.completed";

            // Add metadata for analytics and billing systems
            Metadata = new Dictionary<string, object>
            {
                { "TripDuration", (endTime - startTime).TotalMinutes },
                { "AverageSpeed", distanceTraveled / Math.Max((endTime - startTime).TotalHours, 0.1) },
                { "Currency", "USD" },
                { "CompletedAt", DateTime.UtcNow }
            };
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public string UserEmail { get; private set; }
        public DateTime StartTime { get; private set; }
        public DateTime EndTime { get; private set; }
        public decimal FinalCost { get; private set; }
        public int DistanceTraveled { get; private set; }
        public string StartLocation { get; private set; }
        public string EndLocation { get; private set; }

        public override string ToString()
        {
            return $"TripCompletedIntegrationEvent: BookingId={BookingId}, UserId={UserId}, Distance={DistanceTraveled}km, FinalCost={FinalCost:C}";
        }
    }

    /// <summary>
    /// Integration event published when a vehicle requires maintenance
    /// </summary>
    public class VehicleMaintenanceRequiredIntegrationEvent : IntegrationEvent
    {
        public VehicleMaintenanceRequiredIntegrationEvent(Guid vehicleId, string licensePlate, string vehicleInfo,
                                                        string reason, string currentLocation, int mileage,
                                                        string source = "CarSharingService")
            : base(source)
        {
            VehicleId = vehicleId;
            LicensePlate = licensePlate ?? throw new ArgumentNullException(nameof(licensePlate));
            VehicleInfo = vehicleInfo ?? throw new ArgumentNullException(nameof(vehicleInfo));
            Reason = reason ?? throw new ArgumentNullException(nameof(reason));
            CurrentLocation = currentLocation ?? throw new ArgumentNullException(nameof(currentLocation));
            Mileage = mileage;
            RoutingKey = "vehicle.maintenance.required";

            // Add metadata for maintenance scheduling
            Metadata = new Dictionary<string, object>
            {
                { "Priority", DeterminePriority(reason) },
                { "EstimatedDowntime", EstimateDowntime(reason) },
                { "MaintenanceType", reason }
            };
        }

        public Guid VehicleId { get; private set; }
        public string LicensePlate { get; private set; }
        public string VehicleInfo { get; private set; }
        public string Reason { get; private set; }
        public string CurrentLocation { get; private set; }
        public int Mileage { get; private set; }

        private string DeterminePriority(string reason)
        {
            return reason.ToLower() switch
            {
                var r when r.Contains("safety") || r.Contains("brake") || r.Contains("engine") => "High",
                var r when r.Contains("tire") || r.Contains("oil") => "Medium",
                _ => "Low"
            };
        }

        private int EstimateDowntime(string reason)
        {
            return reason.ToLower() switch
            {
                var r when r.Contains("oil") => 2, // 2 hours
                var r when r.Contains("tire") => 4, // 4 hours
                var r when r.Contains("brake") || r.Contains("engine") => 24, // 24 hours
                _ => 8 // 8 hours default
            };
        }

        public override string ToString()
        {
            return $"VehicleMaintenanceRequiredIntegrationEvent: VehicleId={VehicleId}, LicensePlate={LicensePlate}, Reason={Reason}";
        }
    }

    /// <summary>
    /// Integration event published when payment processing is required
    /// </summary>
    public class PaymentProcessingRequiredIntegrationEvent : IntegrationEvent
    {
        public PaymentProcessingRequiredIntegrationEvent(Guid bookingId, Guid userId, string userEmail,
                                                       decimal amount, string paymentReason,
                                                       string source = "CarSharingService")
            : base(source)
        {
            BookingId = bookingId;
            UserId = userId;
            UserEmail = userEmail ?? throw new ArgumentNullException(nameof(userEmail));
            Amount = amount;
            PaymentReason = paymentReason ?? throw new ArgumentNullException(nameof(paymentReason));
            RoutingKey = "payment.processing.required";

            // Add metadata for payment processing
            Metadata = new Dictionary<string, object>
            {
                { "Currency", "USD" },
                { "PaymentType", paymentReason },
                { "DueDate", DateTime.UtcNow.AddDays(7) },
                { "BookingReference", bookingId.ToString() }
            };
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public string UserEmail { get; private set; }
        public decimal Amount { get; private set; }
        public string PaymentReason { get; private set; }

        public override string ToString()
        {
            return $"PaymentProcessingRequiredIntegrationEvent: BookingId={BookingId}, UserId={UserId}, Amount={Amount:C}, Reason={PaymentReason}";
        }
    }
}
