using DDD.DomainLayer;
using DDD.DomainLayer.Events;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace DDD.ApplicationLayer.EventHandlers
{
    /// <summary>
    /// Handler for user registration events
    /// </summary>
    public class UserRegisteredDomainEventHandler : IEventHandler<UserRegisteredDomainEvent>
    {
        private readonly ILogger<UserRegisteredDomainEventHandler> _logger;

        public UserRegisteredDomainEventHandler(ILogger<UserRegisteredDomainEventHandler> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(UserRegisteredDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling UserRegisteredDomainEvent for user {UserId}", domainEvent.UserId);

            try
            {
                // Business logic that should happen when a user registers:
                // 1. Send welcome email
                // 2. Create user profile
                // 3. Initialize user preferences
                // 4. Send verification instructions

                // Simulate sending welcome email
                await SendWelcomeEmailAsync(domainEvent.Email, domainEvent.FirstName, cancellationToken);

                // Simulate creating user profile
                await CreateUserProfileAsync(domainEvent.UserId, cancellationToken);

                _logger.LogInformation("Successfully processed user registration for {UserId}", domainEvent.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling UserRegisteredDomainEvent for user {UserId}: {ErrorMessage}", 
                    domainEvent.UserId, ex.Message);
                throw;
            }
        }

        private async Task SendWelcomeEmailAsync(string email, string firstName, CancellationToken cancellationToken)
        {
            // Simulate email sending
            await Task.Delay(100, cancellationToken);
            _logger.LogInformation("Welcome email sent to {Email} for {FirstName}", email, firstName);
        }

        private async Task CreateUserProfileAsync(Guid userId, CancellationToken cancellationToken)
        {
            // Simulate profile creation
            await Task.Delay(50, cancellationToken);
            _logger.LogInformation("User profile created for {UserId}", userId);
        }
    }

    /// <summary>
    /// Handler for user verification events
    /// </summary>
    public class UserVerifiedDomainEventHandler : IEventHandler<UserVerifiedDomainEvent>
    {
        private readonly IIntegrationEventBus _integrationEventBus;
        private readonly ILogger<UserVerifiedDomainEventHandler> _logger;

        public UserVerifiedDomainEventHandler(
            IIntegrationEventBus integrationEventBus,
            ILogger<UserVerifiedDomainEventHandler> logger)
        {
            _integrationEventBus = integrationEventBus ?? throw new ArgumentNullException(nameof(integrationEventBus));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(UserVerifiedDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling UserVerifiedDomainEvent for user {UserId}", domainEvent.UserId);

            try
            {
                // Business logic for verified users:
                // 1. Enable booking capabilities
                // 2. Send verification confirmation email
                // 3. Publish integration event for other services
                // 4. Update user permissions

                // Send verification confirmation
                await SendVerificationConfirmationAsync(domainEvent.Email, domainEvent.FullName, cancellationToken);

                // Publish integration event for other services (billing, notifications, etc.)
                var integrationEvent = new UserVerifiedIntegrationEvent(
                    domainEvent.UserId,
                    domainEvent.Email,
                    domainEvent.FullName,
                    "N/A") // Phone number would come from user entity
                {
                    CorrelationId = domainEvent.CorrelationId,
                    CausationId = domainEvent.EventId.ToString()
                };

                await _integrationEventBus.PublishAsync(integrationEvent, cancellationToken);

                _logger.LogInformation("Successfully processed user verification for {UserId}", domainEvent.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling UserVerifiedDomainEvent for user {UserId}: {ErrorMessage}", 
                    domainEvent.UserId, ex.Message);
                throw;
            }
        }

        private async Task SendVerificationConfirmationAsync(string email, string fullName, CancellationToken cancellationToken)
        {
            // Simulate email sending
            await Task.Delay(100, cancellationToken);
            _logger.LogInformation("Verification confirmation email sent to {Email} for {FullName}", email, fullName);
        }
    }

    /// <summary>
    /// Handler for user verification rejection events
    /// </summary>
    public class UserVerificationRejectedDomainEventHandler : IEventHandler<UserVerificationRejectedDomainEvent>
    {
        private readonly ILogger<UserVerificationRejectedDomainEventHandler> _logger;

        public UserVerificationRejectedDomainEventHandler(ILogger<UserVerificationRejectedDomainEventHandler> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Handle(UserVerificationRejectedDomainEvent domainEvent, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Handling UserVerificationRejectedDomainEvent for user {UserId}", domainEvent.UserId);

            try
            {
                // Business logic for rejected verification:
                // 1. Send rejection notification email
                // 2. Provide instructions for resubmission
                // 3. Log rejection for compliance

                await SendRejectionNotificationAsync(domainEvent.Email, domainEvent.Reason, cancellationToken);

                _logger.LogInformation("Successfully processed verification rejection for user {UserId} with reason: {Reason}", 
                    domainEvent.UserId, domainEvent.Reason);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling UserVerificationRejectedDomainEvent for user {UserId}: {ErrorMessage}", 
                    domainEvent.UserId, ex.Message);
                throw;
            }
        }

        private async Task SendRejectionNotificationAsync(string email, string reason, CancellationToken cancellationToken)
        {
            // Simulate email sending
            await Task.Delay(100, cancellationToken);
            _logger.LogInformation("Verification rejection email sent to {Email} with reason: {Reason}", email, reason);
        }
    }
}
