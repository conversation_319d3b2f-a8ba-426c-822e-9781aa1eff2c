using System;

namespace DDD.DomainLayer.Examples
{
    /// <summary>
    /// Example domain event that occurs when a user is created
    /// </summary>
    public class UserCreatedDomainEvent : DomainEvent
    {
        public UserCreatedDomainEvent(Guid userId, string email, string firstName, string lastName)
        {
            UserId = userId;
            Email = email ?? throw new ArgumentNullException(nameof(email));
            FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
            LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
        }

        /// <summary>
        /// The ID of the created user
        /// </summary>
        public Guid UserId { get; private set; }

        /// <summary>
        /// The email of the created user
        /// </summary>
        public string Email { get; private set; }

        /// <summary>
        /// The first name of the created user
        /// </summary>
        public string FirstName { get; private set; }

        /// <summary>
        /// The last name of the created user
        /// </summary>
        public string LastName { get; private set; }

        public override string ToString()
        {
            return $"UserCreatedDomainEvent: UserId={UserId}, Email={Email}, Name={FirstName} {LastName}";
        }
    }
}
