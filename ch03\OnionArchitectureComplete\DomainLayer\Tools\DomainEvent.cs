using System;

namespace DDD.DomainLayer
{
    /// <summary>
    /// Base class for all domain events
    /// </summary>
    public abstract class DomainEvent : IDomainEvent
    {
        protected DomainEvent()
        {
            EventId = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
        }

        protected DomainEvent(Guid eventId, DateTime occurredOn)
        {
            EventId = eventId;
            OccurredOn = occurredOn;
        }

        /// <summary>
        /// Unique identifier for the event
        /// </summary>
        public Guid EventId { get; private set; }

        /// <summary>
        /// When the event occurred
        /// </summary>
        public DateTime OccurredOn { get; private set; }

        /// <summary>
        /// Optional correlation ID for tracking related events
        /// </summary>
        public string? CorrelationId { get; set; }

        /// <summary>
        /// Optional causation ID for tracking the cause of this event
        /// </summary>
        public string? CausationId { get; set; }

        /// <summary>
        /// Version of the event schema for evolution purposes
        /// </summary>
        public virtual int Version => 1;

        public override bool Equals(object? obj)
        {
            if (obj is not DomainEvent other)
                return false;

            return EventId == other.EventId;
        }

        public override int GetHashCode()
        {
            return EventId.GetHashCode();
        }

        public static bool operator ==(DomainEvent? left, DomainEvent? right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(DomainEvent? left, DomainEvent? right)
        {
            return !Equals(left, right);
        }
    }
}
