using System;

namespace DDD.DomainLayer.Events
{
    /// <summary>
    /// Domain event raised when a booking is created
    /// </summary>
    public class BookingCreatedDomainEvent : DomainEvent
    {
        public BookingCreatedDomainEvent(Guid bookingId, Guid userId, Guid vehicleId, DateTime startTime, DateTime endTime, decimal estimatedCost)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            StartTime = startTime;
            EndTime = endTime;
            EstimatedCost = estimatedCost;
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public DateTime StartTime { get; private set; }
        public DateTime EndTime { get; private set; }
        public decimal EstimatedCost { get; private set; }

        public override string ToString()
        {
            return $"BookingCreatedDomainEvent: BookingId={BookingId}, UserId={UserId}, VehicleId={VehicleId}, StartTime={StartTime}, EndTime={EndTime}, EstimatedCost={EstimatedCost:C}";
        }
    }

    /// <summary>
    /// Domain event raised when a booking is confirmed
    /// </summary>
    public class BookingConfirmedDomainEvent : DomainEvent
    {
        public BookingConfirmedDomainEvent(Guid bookingId, Guid userId, Guid vehicleId, DateTime startTime, DateTime endTime)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            StartTime = startTime;
            EndTime = endTime;
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public DateTime StartTime { get; private set; }
        public DateTime EndTime { get; private set; }

        public override string ToString()
        {
            return $"BookingConfirmedDomainEvent: BookingId={BookingId}, UserId={UserId}, VehicleId={VehicleId}, StartTime={StartTime}, EndTime={EndTime}";
        }
    }

    /// <summary>
    /// Domain event raised when a booking is cancelled
    /// </summary>
    public class BookingCancelledDomainEvent : DomainEvent
    {
        public BookingCancelledDomainEvent(Guid bookingId, Guid userId, Guid vehicleId, string reason, string cancelledBy)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            Reason = reason ?? throw new ArgumentNullException(nameof(reason));
            CancelledBy = cancelledBy ?? throw new ArgumentNullException(nameof(cancelledBy));
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public string Reason { get; private set; }
        public string CancelledBy { get; private set; }

        public override string ToString()
        {
            return $"BookingCancelledDomainEvent: BookingId={BookingId}, UserId={UserId}, VehicleId={VehicleId}, Reason={Reason}, CancelledBy={CancelledBy}";
        }
    }

    /// <summary>
    /// Domain event raised when a booking is started (user picks up vehicle)
    /// </summary>
    public class BookingStartedDomainEvent : DomainEvent
    {
        public BookingStartedDomainEvent(Guid bookingId, Guid userId, Guid vehicleId, DateTime actualStartTime)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            ActualStartTime = actualStartTime;
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public DateTime ActualStartTime { get; private set; }

        public override string ToString()
        {
            return $"BookingStartedDomainEvent: BookingId={BookingId}, UserId={UserId}, VehicleId={VehicleId}, ActualStartTime={ActualStartTime}";
        }
    }

    /// <summary>
    /// Domain event raised when a booking is completed
    /// </summary>
    public class BookingCompletedDomainEvent : DomainEvent
    {
        public BookingCompletedDomainEvent(Guid bookingId, Guid userId, Guid vehicleId, DateTime actualEndTime, string returnLocation, decimal finalCost)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            ActualEndTime = actualEndTime;
            ReturnLocation = returnLocation ?? throw new ArgumentNullException(nameof(returnLocation));
            FinalCost = finalCost;
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public DateTime ActualEndTime { get; private set; }
        public string ReturnLocation { get; private set; }
        public decimal FinalCost { get; private set; }

        public override string ToString()
        {
            return $"BookingCompletedDomainEvent: BookingId={BookingId}, UserId={UserId}, VehicleId={VehicleId}, ActualEndTime={ActualEndTime}, ReturnLocation={ReturnLocation}, FinalCost={FinalCost:C}";
        }
    }

    /// <summary>
    /// Domain event raised when a user doesn't show up for their booking
    /// </summary>
    public class BookingNoShowDomainEvent : DomainEvent
    {
        public BookingNoShowDomainEvent(Guid bookingId, Guid userId, Guid vehicleId, DateTime scheduledStartTime)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            ScheduledStartTime = scheduledStartTime;
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public DateTime ScheduledStartTime { get; private set; }

        public override string ToString()
        {
            return $"BookingNoShowDomainEvent: BookingId={BookingId}, UserId={UserId}, VehicleId={VehicleId}, ScheduledStartTime={ScheduledStartTime}";
        }
    }

    /// <summary>
    /// Domain event raised when a booking is extended
    /// </summary>
    public class BookingExtendedDomainEvent : DomainEvent
    {
        public BookingExtendedDomainEvent(Guid bookingId, Guid userId, Guid vehicleId, DateTime oldEndTime, DateTime newEndTime, decimal additionalCost)
        {
            BookingId = bookingId;
            UserId = userId;
            VehicleId = vehicleId;
            OldEndTime = oldEndTime;
            NewEndTime = newEndTime;
            AdditionalCost = additionalCost;
        }

        public Guid BookingId { get; private set; }
        public Guid UserId { get; private set; }
        public Guid VehicleId { get; private set; }
        public DateTime OldEndTime { get; private set; }
        public DateTime NewEndTime { get; private set; }
        public decimal AdditionalCost { get; private set; }

        public override string ToString()
        {
            return $"BookingExtendedDomainEvent: BookingId={BookingId}, UserId={UserId}, VehicleId={VehicleId}, EndTime: {OldEndTime} -> {NewEndTime}, AdditionalCost={AdditionalCost:C}";
        }
    }
}
