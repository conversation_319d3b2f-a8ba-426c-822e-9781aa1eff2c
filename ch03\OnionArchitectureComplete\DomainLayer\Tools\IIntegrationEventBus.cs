using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DDD.DomainLayer
{
    /// <summary>
    /// Interface for publishing integration events to external systems
    /// </summary>
    public interface IIntegrationEventBus
    {
        /// <summary>
        /// Publish an integration event to the event bus
        /// </summary>
        /// <param name="integrationEvent">The integration event to publish</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        Task PublishAsync(IIntegrationEvent integrationEvent, CancellationToken cancellationToken = default);

        /// <summary>
        /// Publish multiple integration events to the event bus
        /// </summary>
        /// <param name="integrationEvents">The integration events to publish</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        Task PublishAsync(IEnumerable<IIntegrationEvent> integrationEvents, CancellationToken cancellationToken = default);
    }
}
