﻿using System.Threading.Tasks;
using System.Data;
using System.Threading;
using System.Collections.Generic;

namespace DDD.DomainLayer
{
    /// <summary>
    /// Unit of Work pattern interface for managing transactions and domain events
    /// </summary>
    public interface IUnitOfWork
    {
        /// <summary>
        /// Save all pending changes and dispatch domain events
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if any entities were saved</returns>
        Task<bool> SaveEntitiesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Start a new transaction with the specified isolation level
        /// </summary>
        /// <param name="isolationLevel">The isolation level for the transaction</param>
        /// <param name="cancellationToken">Cancellation token</param>
        Task StartAsync(IsolationLevel isolationLevel = IsolationLevel.ReadCommitted, CancellationToken cancellationToken = default);

        /// <summary>
        /// Commit the current transaction
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task CommitAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Rollback the current transaction
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task RollbackAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Get all domain events from tracked entities
        /// </summary>
        /// <returns>Collection of domain events</returns>
        IEnumerable<IDomainEvent> GetDomainEvents();

        /// <summary>
        /// Clear domain events from all tracked entities
        /// </summary>
        void ClearDomainEvents();

        /// <summary>
        /// Check if there are any pending domain events
        /// </summary>
        bool HasDomainEvents { get; }
    }
}
