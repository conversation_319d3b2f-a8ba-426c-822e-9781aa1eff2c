using System;

namespace DDD.DomainLayer.Examples
{
    /// <summary>
    /// Example integration event that notifies other services when a user is registered
    /// </summary>
    public class UserRegisteredIntegrationEvent : IntegrationEvent
    {
        public UserRegisteredIntegrationEvent(Guid userId, string email, string firstName, string lastName, string source = "UserService")
            : base(source)
        {
            UserId = userId;
            Email = email ?? throw new ArgumentNullException(nameof(email));
            FirstName = firstName ?? throw new ArgumentNullException(nameof(firstName));
            LastName = lastName ?? throw new ArgumentNullException(nameof(lastName));
            RoutingKey = "user.registered";
        }

        /// <summary>
        /// The ID of the registered user
        /// </summary>
        public Guid UserId { get; private set; }

        /// <summary>
        /// The email of the registered user
        /// </summary>
        public string Email { get; private set; }

        /// <summary>
        /// The first name of the registered user
        /// </summary>
        public string FirstName { get; private set; }

        /// <summary>
        /// The last name of the registered user
        /// </summary>
        public string LastName { get; private set; }

        public override string ToString()
        {
            return $"UserRegisteredIntegrationEvent: UserId={UserId}, Email={Email}, Name={FirstName} {LastName}, Source={Source}";
        }
    }
}
